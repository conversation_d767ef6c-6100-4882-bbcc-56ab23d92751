package progression

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand/v2"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/configs"
	"wildlight.gg/bluejay/entitlements"
	"wildlight.gg/bluejay/storage"
	"wildlight.gg/bluejay/utils"
)

type CompositeStat struct {
	Name      string
	Value     float64
	TimeStamp int64
}

type UserStats struct {
	Stats map[string]CompositeStat
}

type UserChallenges struct {
	Challenges map[string]ChallegeGroupProgressInfo
}

type CompositeMatchStat struct {
	Name              string
	Value             float64
	StatsWithLocalTag map[string]float64
	TimeStamp         int64
}

type PreviousMatchStorageWrapper struct {
	LastMatchStats      []CompositeMatchStat
	ChallengesPreUpdate map[string]ChallengeProgress
	GameModeString      string
	MapName             string
	MatchResult         int32
}

type CompositeStatPayload struct {
	AllStats []CompositeStat
}

type MatchRewards struct {
	CurrencyRewards    map[string]int64
	EntitlementRewards []string
}

type RewardPayload struct {
	PlayerID string
	Rewards  MatchRewards
}

type ChallengeProgressInfo struct {
	TierIndex  int
	Progress   float64
	IsComplete bool
}

type ChallegeGroupProgressInfo struct {
	ChallengeID string
	Challenges  map[int]ChallengeProgressInfo
	IsComplete  bool
}

type LastMatchStorageWrapper struct {
	LastMatchStats      []CompositeMatchStat
	ChallengesPreUpdate []ChallegeGroupProgressInfo
	GameModeString      string
	MapName             string
	MatchResult         int32
}

type FullMatchStatsAndRewardsPayload struct {
	Rewards             MatchRewards
	AllStats            []CompositeStat
	MatchStats          []CompositeMatchStat
	Challenges          []ChallegeGroupProgressInfo
	ChallengesPreUpdate []ChallegeGroupProgressInfo
	GameModeString      string
	MapName             string
	MatchResult         int32
	PlayerID            string
}

type FullMatchStatsAndRewards struct {
	Stats                        []CompositeStat
	LastMatchStats               []CompositeMatchStat
	LastMatchChallengesPreUpdate []ChallegeGroupProgressInfo
	Challenges                   []ChallegeGroupProgressInfo
	LastMatchMapName             string
	LastMatchGameMode            string
	LastMatchResult              int32
}

type MatchInfoPayload struct {
	Rewards             MatchRewards
	AllStats            []CompositeStat
	MatchStats          []CompositeMatchStat
	Challenges          map[string]ChallengeProgress
	ChallengesPreUpdate map[string]ChallengeProgress
	GameModeString      string
	MapName             string
	MatchResult         int32
	PlayerID            string
}

type SlotInformation struct {
	SlotInformation map[string]string
}

type FullStatsAndChallengeInfo struct {
	Stats                        []CompositeStat
	LastMatchStats               []CompositeMatchStat
	LastMatchChallengesPreUpdate map[string]ChallengeProgress
	Challenges                   map[string]ChallengeProgress
	LastMatchMapName             string
	LastMatchGameMode            string
	LastMatchResult              int32
	PoolAndSlotInfo              map[string]SlotInformation
	ActiveQuestName              string
}

type PoolConfig struct {
	Challenges    []string
	History       int
	Refresh       int64
	SelectionMode string
	ResetTime     string
	ResetDay      string
}

type PoolsConfig map[string]PoolConfig

type SlotConfigEntry struct {
	SlotName      string
	ChallengePool string
}

type SlotsConfig []SlotConfigEntry

type ActiveChallenge struct {
	Name       string
	Progress   int64
	Expiry     int64
	Completed  bool
	InstanceID string
}

type ChallengeProgress struct {
	Tier          int
	Progress      int64
	MaxProgress   int64
	Completed     bool
	ChallengeName string
	Expiry        int64
}

type ActiveChallenges struct {
	Challenges map[string]ActiveChallenge
}

type HistoryEntry struct {
	Name      string
	Timestamp int64
}

type PoolHistory struct {
	Entries         []HistoryEntry
	NextLinearIndex int
}

type ChallengeHistory struct {
	Pools map[string]PoolHistory
}

type ChallengesConfig struct {
	Pools           PoolsConfig
	Slots           SlotsConfig
	ActiveQuestName string
	AssetNames      map[string]string
}

type CompleteChallengeRequest struct {
	Challenges []string
}

const (
	CHALLENGE_ASSIGNED       string = "new_challenge"
	CHALLENGE_COMPLETED      string = "completed"
	CHALLENGE_EXPIRED        string = "expired"
	CHALLENGE_PROGRESSED     string = "progress_updated"
	CHALLENGE_UPDATE_EVENT   string = "challengeUpdate"
	ENTITLEMENT_UPDATE_EVENT string = "entitlementUpdate"
	MATCH_REWARD             string = "reward"
	WALLET_UPDATE_EVENT      string = "walletUpdate"
)

var (
	resetTimeCache struct {
		sync.RWMutex
		poolResets map[string]resetTimeInfo
	}
)

type resetTimeInfo struct {
	nextResetTime time.Time
	resetDay      time.Weekday
	resetTime     time.Time
	configured    bool
}

var ChallengesConfigWatcher *configs.ConfigWatcher[ChallengesConfig]
var ChallengesConfigPath = "config/game-configs/challenges/"

func InitProgressionConfigWatcher(nk runtime.NakamaModule, logger runtime.Logger) {
	ChallengesConfigWatcher = configs.NewConfigWatcher(ChallengesConfigPath, onConfigLoaded, logger)
	resetTimeCache.poolResets = map[string]resetTimeInfo{}
}

func onConfigLoaded(env string, config ChallengesConfig) error {

	return nil
}

func getNextResetTime(currentTime time.Time, poolName string, poolRefresh int64, resetDay string, resetTimeStr string, logger runtime.Logger) (time.Time, error) {

	resetTimeCache.RLock()
	poolResetInfo, exists := resetTimeCache.poolResets[poolName]

	if exists && poolResetInfo.configured {
		if poolResetInfo.nextResetTime.After(currentTime) {
			nextReset := poolResetInfo.nextResetTime
			resetTimeCache.RUnlock()
			return nextReset, nil
		}

		resetTimeCache.RUnlock()

		return calculateNextResetTime(currentTime, poolName, poolRefresh, resetDay, resetTimeStr, logger)

	}

	resetTimeCache.RUnlock()
	return calculateNextResetTime(currentTime, poolName, poolRefresh, resetDay, resetTimeStr, logger)

}

func calculateNextResetTime(currentTime time.Time, poolName string, poolRefresh int64, resetDay string, resetTimeStr string, logger runtime.Logger) (time.Time, error) {

	resetDay = strings.TrimSpace(resetDay)

	logger.WithField("resetDay", resetDay).Info("Setting reset on this day.")

	resetDayEnum, err := parseWeekday(resetDay)

	logger.WithField("resetDayEnum", resetDayEnum).Info("Setting reset on this day.")

	if err != nil {
		return time.Time{}, err
	}

	resetTime, err := time.Parse(time.RFC3339, resetTimeStr)

	logger.WithField("resetTime", resetTime).Info("Setting reset on this time.")

	if err != nil {
		return time.Time{}, err
	}

	info := resetTimeInfo{
		resetDay:   resetDayEnum,
		resetTime:  resetTime,
		configured: true,
	}

	daysToSubtract := (int(currentTime.Weekday()) - int(resetDayEnum) + 7) % 7

	mostRecentResetDay := currentTime.AddDate(0, 0, -daysToSubtract)

	logger.WithField("mostRecentResetDay", mostRecentResetDay).WithField("currentTime.Weekday()", currentTime.Weekday()).WithField("resetDayEnum", resetDayEnum).WithField("daysToSubtract", daysToSubtract).Info("Days offset to reset day from current day and most recent reset day from current day")

	loc := resetTime.Location()
	hour, min, sec := resetTime.Clock()
	startingPoint := time.Date(mostRecentResetDay.Year(), mostRecentResetDay.Month(), mostRecentResetDay.Day(), hour, min, sec, 0, loc)

	startUnix := startingPoint.Unix()
	currentUnix := currentTime.Unix()

	logger.WithField("loc", loc).WithField("startingPoint", startingPoint).Info("Starting point and timezone")

	logger.WithField("startUnix", startUnix).WithField("currentUnix", currentUnix).Info("Current time and starting time in unix")
	if startUnix > currentUnix {
		info.nextResetTime = startingPoint

		resetTimeCache.Lock()
		resetTimeCache.poolResets[poolName] = info
		resetTimeCache.Unlock()

		logger.WithField("info", info).WithField("startingPoint", startingPoint).Info("reset time info")

		return startingPoint, nil
	}

	elapsedSeconds := currentUnix - startUnix
	intervals := elapsedSeconds / poolRefresh
	nextInterval := intervals + 1
	nextResetUnix := startUnix + (nextInterval * poolRefresh)
	nextReset := time.Unix(nextResetUnix, 0).In(loc)

	logger.WithField("elapsedSeconds", elapsedSeconds).WithField("intervals", intervals).WithField("nextInterval", nextInterval).WithField("nextResetUnix", nextResetUnix).WithField("nextReset", nextReset).Info("Next reset time. And all calculations.")

	info.nextResetTime = nextReset

	resetTimeCache.Lock()
	resetTimeCache.poolResets[poolName] = info
	resetTimeCache.Unlock()

	return nextReset, nil

}

func parseWeekday(resetDay string) (time.Weekday, error) {

	resetDay = strings.ToLower(resetDay)

	switch resetDay {
	case "sun", "sunday":
		return time.Sunday, nil
	case "mon", "monday":
		return time.Monday, nil
	case "tue", "tuesday":
		return time.Tuesday, nil
	case "wed", "wednesday":
		return time.Wednesday, nil
	case "thu", "thursday":
		return time.Thursday, nil
	case "fri", "friday":
		return time.Friday, nil
	case "sat", "saturday":
		return time.Saturday, nil

	}

	return time.Sunday, runtime.NewError("Invalid reset day string in pool config.", 3)
}

func loadActiveChallenges(ctx context.Context, nk runtime.NakamaModule, userID string) (ActiveChallenges, string, error) {
	storageRead := &runtime.StorageRead{
		Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
		Key:        storage.ACTIVE_CHALLENGES,
		UserID:     userID,
	}

	readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{storageRead})

	active := ActiveChallenges{Challenges: map[string]ActiveChallenge{}}

	if err != nil {
		return active, "", err
	}

	if len(readResult) == 0 {
		return active, "", nil
	}

	if len(readResult) != 1 {
		return active, "", runtime.NewError(fmt.Sprintf("More than one challenges result found. Must investigate for user id %s", userID), 3)
	}

	result := readResult[0]

	err = json.Unmarshal([]byte(result.Value), &active)

	if err != nil {
		return active, "", err
	}

	return active, result.Version, err
}

func loadChallengeHistory(ctx context.Context, nk runtime.NakamaModule, userID string) (ChallengeHistory, string, error) {
	storageRead := &runtime.StorageRead{
		Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
		Key:        storage.CHALLENGE_HISTORY,
		UserID:     userID,
	}

	readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{storageRead})

	history := ChallengeHistory{Pools: map[string]PoolHistory{}}

	if err != nil {
		return history, "", err
	}

	if len(readResult) == 0 {
		return history, "", nil
	}

	if len(readResult) != 1 {
		return history, "", runtime.NewError(fmt.Sprintf("More than one challenges history found. Must investigate for user id %s", userID), 3)
	}

	result := readResult[0]

	err = json.Unmarshal([]byte(result.Value), &history)

	if err != nil {
		return history, "", err
	}

	return history, result.Version, err
}

func initPoolAssignments(active ActiveChallenges, slots SlotsConfig, logger runtime.Logger) map[string]map[string]bool {
	// Keep track of which challenges per challenges pool are assigned.
	assignments := map[string]map[string]bool{}

	for slotName, challenge := range active.Challenges {
		for _, slotConfig := range slots {
			if slotConfig.SlotName == slotName {
				pool := slotConfig.ChallengePool

				if _, exists := assignments[pool]; !exists {
					assignments[pool] = map[string]bool{}
				}

				assignments[pool][challenge.Name] = true
				break
			}
		}

	}

	return assignments
}

func processExpiredChallenges(ctx context.Context, slots SlotsConfig, pools PoolsConfig, active ActiveChallenges, assignments map[string]map[string]bool, serverTime time.Time, logger runtime.Logger, challengeEvents *[]any) (bool, error) {

	updated := false

	challengesConfig, err := ChallengesConfigWatcher.GetConfigForEnvironment(utils.GetPlayerEnvironment(ctx))

	if err != nil {
		logger.WithField("error", err).Error("Got error loading config")
		return false, err
	}

	for slotName, challenge := range active.Challenges {
		for _, slotsConfig := range slots {
			if slotsConfig.SlotName == slotName {
				pool := slotsConfig.ChallengePool
				poolConfig, exists := pools[pool]

				if exists && poolConfig.SelectionMode == "linear" {

					if challenge.Completed {
						// Remove challenge from active challenges
						delete(active.Challenges, slotName)
						updated = true

						// Remove challenge from assigned challenges
						if assignment, exists := assignments[pool]; exists {
							delete(assignment, challenge.Name)
						}

						assetName := challengesConfig.AssetNames[challenge.Name]

						challengeEvent := utils.ChallengeEvent{ID: challenge.InstanceID, ChallengeName: assetName, Reason: CHALLENGE_EXPIRED, CurrentProgress: int(challenge.Progress), Completed: challenge.Completed}

						*challengeEvents = append(*challengeEvents, challengeEvent)

						logger.WithField("slotName", slotName).WithField("challengeName", challenge.Name).WithField("serverTime", serverTime).WithField("expiry", challenge.Expiry).Info("Challenge has expired.")
					}
				} else {

					if serverTime.Unix() > challenge.Expiry {
						// Remove challenge from active challenges
						delete(active.Challenges, slotName)
						updated = true

						assetName := challengesConfig.AssetNames[challenge.Name]

						challengeEvent := utils.ChallengeEvent{ID: challenge.InstanceID, ChallengeName: assetName, Reason: CHALLENGE_EXPIRED, CurrentProgress: int(challenge.Progress), Completed: challenge.Completed}

						*challengeEvents = append(*challengeEvents, challengeEvent)

						// Remove challenge from assigned challenges
						if assignment, exists := assignments[pool]; exists {
							delete(assignment, challenge.Name)
						}

						logger.WithField("slotName", slotName).WithField("challengeName", challenge.Name).WithField("serverTime", serverTime).WithField("expiry", challenge.Expiry).Info("Challenge has expired.")
					}
				}
			}
		}
	}

	return updated, nil
}

func assignNewChallenge(ctx context.Context, slots SlotsConfig, pools PoolsConfig, active ActiveChallenges, history ChallengeHistory, assignements map[string]map[string]bool, serverTime time.Time, logger runtime.Logger, challengeEvents *[]any) (bool, error) {

	updated := false

	// process each slot and go through each pool of challenges
	for _, slotConfigEntry := range slots {
		slotName := slotConfigEntry.SlotName
		poolName := slotConfigEntry.ChallengePool
		logger.WithField("slotName", slotName).WithField("poolName", poolName).Info("Processing slot and pool.")
		poolConfig, exists := pools[poolName]
		if !exists {
			// This is actually very problematic - If we have a slot using a name for a pool that is not set up
			logger.WithField("slotName", slotName).WithField("poolName", poolName).Error("Pool config does not exist, but used in slot.")
			continue
		}

		// Check if we have an active challenge for this slot already
		// if we do, then we can just skip it
		activeChallenge, hasActiveChallenge := active.Challenges[slotName]
		if hasActiveChallenge {
			logger.WithField("slotName", slotName).WithField("activeChallenge", activeChallenge).Info("Slot has active challenge already")
			continue
		}

		// Grab the history for this pool
		poolHistory, exists := history.Pools[poolName]

		if !exists {
			poolHistory = PoolHistory{
				Entries:         []HistoryEntry{},
				NextLinearIndex: 0,
			}
		}

		var err error = nil
		// Process generating a linear or random challenge
		if poolConfig.SelectionMode == "linear" {
			updated = assignLinearChallenge(ctx, active, &history, assignements, slotName, poolName, poolConfig, &poolHistory, logger, challengeEvents) || updated
		} else {
			updated, err = assignRandomChallenge(ctx, active, &history, assignements, slotName, poolName, poolConfig, &poolHistory, serverTime, logger, challengeEvents)

			if err != nil {
				return false, err
			}
		}

	}

	return updated, nil
}

func saveUpdatedChallengeData(ctx context.Context, nk runtime.NakamaModule, userID string, active ActiveChallenges, activeVersion string, history ChallengeHistory, historyVersion string) error {

	activeJSON, err := json.Marshal(active)

	if err != nil {
		return err
	}

	historyJSON, err := json.Marshal(history)

	if err != nil {
		return err
	}

	storageWrites := []*runtime.StorageWrite{}

	if len(active.Challenges) != 0 {
		storageWrites = append(storageWrites, &runtime.StorageWrite{
			Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
			Key:        storage.ACTIVE_CHALLENGES,
			UserID:     userID,
			Value:      string(activeJSON),
			Version:    activeVersion,
		})
	}

	if len(history.Pools) != 0 {
		storageWrites = append(storageWrites, &runtime.StorageWrite{
			Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
			Key:        storage.CHALLENGE_HISTORY,
			UserID:     userID,
			Value:      string(historyJSON),
			Version:    historyVersion,
		})
	}

	if len(storageWrites) != 0 {
		_, err = nk.StorageWrite(ctx, storageWrites)
	}

	return err
}

func assignRandomChallenge(ctx context.Context, active ActiveChallenges, challengeHistory *ChallengeHistory, assignements map[string]map[string]bool, slotName, poolName string, poolConfig PoolConfig, poolHistory *PoolHistory, serverTime time.Time, logger runtime.Logger, challengeEvents *[]any) (bool, error) {
	currentlyAssigned := map[string]bool{}

	poolResetTime := poolConfig.ResetTime
	poolRefreshSeconds := poolConfig.Refresh
	poolResetDay := poolConfig.ResetDay

	resetTime, err := getNextResetTime(serverTime, poolName, poolRefreshSeconds, poolResetDay, poolResetTime, logger)

	if err != nil {
		logger.WithField("resetTime", poolResetTime).Error("Unable to calculate reset time for challenges.")
		return false, err
	}
	// Track currently assigned challenges
	if assignement, exists := assignements[poolName]; exists {
		for challenge := range assignement {
			currentlyAssigned[challenge] = true
		}
	}

	logger.WithField("currentlyAssigned", currentlyAssigned).Info("Currently assigned challenges.")

	// Grab available challenges not assigned, or recently used
	availableChallenges := filterAvailableChallenges(poolConfig.Challenges, poolHistory.Entries, poolConfig.History, currentlyAssigned, logger)

	if len(availableChallenges) == 0 {
		logger.Warn("No available challenges found")
		return false, nil
	}

	// grab random challenge
	newChallengeName := availableChallenges[rand.IntN(len(availableChallenges))]
	expiryTime := calcExpiryTime(serverTime.Unix(), poolConfig.Refresh, resetTime.Unix(), logger)

	logger.WithField("newChallengeName", newChallengeName).WithField("expiryTime", expiryTime).Info("New challenge name with new expiry time")

	instanceId, err := uuid.NewV7()

	if err != nil {
		logger.Error("Error generating instance ID for new challenge")
		return false, err
	}
	// Create new challenge entry
	activeChallenge := ActiveChallenge{
		Name:       newChallengeName,
		Expiry:     expiryTime,
		Progress:   0,
		InstanceID: instanceId.String(),
	}

	active.Challenges[slotName] = activeChallenge

	if _, exists := assignements[poolName]; !exists {
		assignements[poolName] = map[string]bool{}
	}

	assignements[poolName][newChallengeName] = true

	// Only store history if we are storing more than "0" days. 0 = no history
	if poolConfig.History > 0 {
		newEntry := HistoryEntry{
			Name:      newChallengeName,
			Timestamp: serverTime.Unix(),
		}

		poolHistory.Entries = append(poolHistory.Entries, newEntry)
		if len(poolHistory.Entries) > poolConfig.History {
			poolHistory.Entries = poolHistory.Entries[len(poolHistory.Entries)-poolConfig.History:]
		}

		challengeHistory.Pools[poolName] = *poolHistory
	}

	challengesConfig, err := ChallengesConfigWatcher.GetConfigForEnvironment(utils.GetPlayerEnvironment(ctx))

	if err != nil {
		logger.WithField("error", err).Error("Got error loading config")
		return false, err
	}

	assetName := challengesConfig.AssetNames[activeChallenge.Name]

	challengeEvent := utils.ChallengeEvent{ID: activeChallenge.InstanceID, ChallengeName: assetName, Reason: CHALLENGE_ASSIGNED, CurrentProgress: int(activeChallenge.Progress), Completed: activeChallenge.Completed}

	*challengeEvents = append(*challengeEvents, challengeEvent)

	logger.WithField("newChallengeName", newChallengeName).WithField("slotName", slotName).WithField("poolName", poolName).WithField("expiry", expiryTime).WithField("active", active).WithField("history", *challengeHistory).Info("New challenge assigned.")

	return true, nil

}

func filterAvailableChallenges(allChallenges []string, history []HistoryEntry, historyCount int, assigned map[string]bool, logger runtime.Logger) []string {
	recent := map[string]bool{}

	// Track recently used challenges
	if historyCount > 0 && len(history) > 0 {
		recentCount := int(math.Min(float64(len(history)), float64(historyCount)))
		recentHistory := history[len(history)-recentCount:]

		for _, entry := range recentHistory {
			recent[entry.Name] = true
		}
	}

	logger.WithField("recent", recent).WithField("history", history).Info("Recently assigned challenges.")

	available := []string{}

	// Filter out challenges not recently used or currently assigned
	for _, challenge := range allChallenges {
		if !recent[challenge] && !assigned[challenge] {
			available = append(available, challenge)
		}
	}

	logger.WithField("available", available).Info("Available challenges not recently or currently assigned.")

	return available
}

func assignLinearChallenge(ctx context.Context, active ActiveChallenges, history *ChallengeHistory, assignments map[string]map[string]bool, slotName string, poolName string, poolConfig PoolConfig, poolHistory *PoolHistory, logger runtime.Logger, challengeEvents *[]any) bool {
	updated := false

	logger.WithField("slotName", slotName).WithField("poolName", poolName).Info("Assigning linear challenge.")

	// If we are at the end of the linear slots to assign, we will just return out since there are no more challenges remaining
	if poolHistory.NextLinearIndex >= len(poolConfig.Challenges) {
		logger.Warn("No unassigned challenges remaining")
		return updated
	}

	currentlyAssigned := map[string]bool{}

	// if we have assigned challenges for this pool, lets use those to process
	if assignement, exists := assignments[poolName]; exists {
		currentlyAssigned = assignement
	}

	logger.WithField("currentlyAssigned", currentlyAssigned).Info("Currently assigned challenges.")

	foundUnassigned := false

	// we are keep track of the next index to use in the history object so we dont have to iterate through the whole list each time
	startIndex := poolHistory.NextLinearIndex
	var newChallengeName string
	var newChallengIndex int

	logger.WithField("poolHistory.NextLinearIndex", poolHistory.NextLinearIndex).Info("Next index to be assigned.")

	for i := range poolConfig.Challenges {
		candidateIndex := startIndex + i
		candidateChallenge := poolConfig.Challenges[candidateIndex]

		// if the challenge at the next available index is not already assigned, lets grab that to assign
		if !currentlyAssigned[candidateChallenge] {
			newChallengIndex = candidateIndex
			newChallengeName = candidateChallenge

			poolHistory.NextLinearIndex = candidateIndex + 1
			foundUnassigned = true
			logger.WithField("newChallengIndex", newChallengIndex).WithField("newChallengeName", newChallengeName).Info("found new challenge ")
			break
		}
	}

	if !foundUnassigned {
		logger.Warn("No unassigned challenges remaining")
		return updated
	}

	instanceId, err := uuid.NewUUID()

	if err != nil {
		logger.Error("Error generating instance ID for new challenge")
		return false
	}
	// Set up active challenge object to track it
	activeChallenge := ActiveChallenge{
		Name:       newChallengeName,
		Progress:   0,
		InstanceID: instanceId.String(),
	}

	// Mark the slot with the latest active challenge
	active.Challenges[slotName] = activeChallenge

	if _, exists := assignments[poolName]; !exists {
		assignments[poolName] = map[string]bool{}
	}

	// Track the challenge as assigned
	assignments[poolName][newChallengeName] = true

	history.Pools[poolName] = *poolHistory

	updated = true

	challengesConfig, err := ChallengesConfigWatcher.GetConfigForEnvironment(utils.GetPlayerEnvironment(ctx))

	assetName := challengesConfig.AssetNames[activeChallenge.Name]

	challengeEvent := utils.ChallengeEvent{ID: activeChallenge.InstanceID, ChallengeName: assetName, Reason: CHALLENGE_ASSIGNED, CurrentProgress: int(activeChallenge.Progress), Completed: activeChallenge.Completed}

	*challengeEvents = append(*challengeEvents, challengeEvent)

	logger.WithField("newChallengeName", newChallengeName).WithField("newChallengeIndex", newChallengIndex).WithField("slotName", slotName).WithField("poolName", poolName).WithField("active", active).WithField("history", *history).Info("New challenge assigned.")

	return updated
}

func calcExpiryTime(serverTime, refreshSeconds, nextResetTime int64, logger runtime.Logger) int64 {

	rawExpiry := serverTime + refreshSeconds

	logger.WithField("rawExpiry", rawExpiry).WithField("serverTime", serverTime).WithField("refreshSeconds", refreshSeconds).WithField("nextResetTime", nextResetTime).Info("Calculating next reset time.")
	if nextResetTime > serverTime && nextResetTime < rawExpiry {
		return nextResetTime
	}

	return rawExpiry
}

func capCurrencyReward(ctx context.Context, nk runtime.NakamaModule, logger runtime.Logger, userID string, changeset map[string]int64) (map[string]int64, error) {
	account, err := nk.AccountGetId(ctx, userID)

	wallet := map[string]int64{}
	result := map[string]int64{}

	if err != nil {
		logger.Error(fmt.Sprintf("Got error while loading account info: %s", err.Error()))
		return result, err
	}

	if err := json.Unmarshal([]byte(account.Wallet), &wallet); err != nil {
		logger.Error(fmt.Sprintf("Got error while loading json wallet info: %s", err.Error()))

		return result, err
	}

	for currency, amount := range changeset {
		currentAmount := wallet[currency]

		storeConfig, err := configs.LoadStoreConfig(ctx)

		if err != nil {
			logger.Error(fmt.Sprintf("Got error while loading store config: %s", err.Error()))
			return result, err
		}

		if max, exists := storeConfig.CurrencyMaxLimits[currency]; !exists {
			result[currency] = amount
		} else {
			remaining := max - currentAmount
			if remaining <= 0 {
				result[currency] = 0
			} else if amount <= remaining {
				result[currency] = amount
			} else {
				result[currency] = remaining
			}
		}
	}

	return result, nil
}

func createRewardUpdates(ctx context.Context, nk runtime.NakamaModule, reward MatchRewards, userID string, logger runtime.Logger) (*[]*runtime.WalletUpdate, *[]*runtime.StorageWrite, error) {

	walletUpdates := []*runtime.WalletUpdate{}
	storageUpdates := []*runtime.StorageWrite{}

	if len(reward.CurrencyRewards) > 0 {

		changeset, err := capCurrencyReward(ctx, nk, logger, userID, reward.CurrencyRewards)
		if err != nil {
			return nil, nil, err
		}
		walletUpdate := runtime.WalletUpdate{
			UserID:    userID,
			Changeset: changeset,
		}

		logger.Info("CurrencyRewards storage write %v ", walletUpdate)
		walletUpdates = append(walletUpdates, &walletUpdate)
	}

	if len(reward.EntitlementRewards) > 0 {
		userEntitlements, version, err := entitlements.ReadEntitlementsAsMap(ctx, nk, userID)

		if err != nil {
			return nil, nil, err
		}

		if userEntitlements.Entitlements == nil {
			userEntitlements.Entitlements = map[string]any{}
		}

		for _, entitlement := range reward.EntitlementRewards {
			userEntitlements.Entitlements[entitlement] = struct{}{}
		}

		newEntitlements, err := json.Marshal(userEntitlements)

		if err != nil {
			return nil, nil, err
		}

		storageWrite := &runtime.StorageWrite{
			Collection:      storage.ENTITLEMENT_COLLECTION,
			Key:             storage.ENTITLEMENTS,
			UserID:          userID,
			PermissionRead:  1,
			PermissionWrite: 1,
			Value:           string(newEntitlements),
			Version:         version,
		}

		storageUpdates = append(storageUpdates, storageWrite)
	}

	return &walletUpdates, &storageUpdates, nil
}

func readStats(ctx context.Context, nk runtime.NakamaModule, userID string) (UserStats, string, error) {
	storageRead := &runtime.StorageRead{
		Collection: storage.PROGRESSION_STATS_COLLECTION,
		Key:        storage.PROGRESSION_STATS,
		UserID:     userID,
	}

	readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{storageRead})

	if err != nil {
		return UserStats{}, "", err
	}

	if len(readResult) == 0 {
		return UserStats{}, "", nil
	}

	if len(readResult) != 1 {
		return UserStats{}, "", runtime.NewError(fmt.Sprintf("More than one stats result found. Must investigate for user id %s", userID), 3)
	}

	result := readResult[0]

	userStats := UserStats{}

	err = json.Unmarshal([]byte(result.Value), &userStats)

	if err != nil {
		return UserStats{}, "", err
	}

	return userStats, result.Version, err
}

func readChallenges(ctx context.Context, nk runtime.NakamaModule, userID string) (UserChallenges, string, error) {
	storageRead := &runtime.StorageRead{
		Collection: storage.PROGRESSION_CHALLENGES_COLLECTION,
		Key:        storage.PROGRESSION_CHALLENGES,
		UserID:     userID,
	}

	readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{storageRead})

	if err != nil {
		return UserChallenges{}, "", err
	}

	if len(readResult) == 0 {
		return UserChallenges{}, "", nil
	}

	if len(readResult) != 1 {
		return UserChallenges{}, "", runtime.NewError(fmt.Sprintf("More than one challenges result found. Must investigate for user id %s", userID), 3)
	}

	result := readResult[0]

	userChallenges := UserChallenges{}

	err = json.Unmarshal([]byte(result.Value), &userChallenges)

	if err != nil {
		return UserChallenges{}, "", err
	}

	return userChallenges, result.Version, err
}

func LoadStatsAndChallengesForUserID(ctx context.Context, nk runtime.NakamaModule, userID string, logger runtime.Logger) (FullStatsAndChallengeInfo, error) {

	userStats, _, err := readStats(ctx, nk, userID)

	if err != nil {
		return FullStatsAndChallengeInfo{}, err
	}

	readRequest := runtime.StorageRead{
		Collection: storage.PROGRESSION_MATCH_COLLECTION,
		Key:        storage.USER_PREVIOUS_MATCH,
		UserID:     userID,
	}

	readRequests := []*runtime.StorageRead{&readRequest}

	lastMatchObject, err := nk.StorageRead(ctx, readRequests)

	if err != nil {
		return FullStatsAndChallengeInfo{}, err
	}

	var lastMatchStats PreviousMatchStorageWrapper = PreviousMatchStorageWrapper{LastMatchStats: []CompositeMatchStat{}, ChallengesPreUpdate: map[string]ChallengeProgress{}, GameModeString: "", MapName: "", MatchResult: -1}

	if len(lastMatchObject) != 0 {
		jsonStat := lastMatchObject[0]
		if err := json.Unmarshal([]byte(jsonStat.Value), &lastMatchStats); err != nil {
			logger.WithField("lastMatchObject", jsonStat.Value).Error("Error with json unmarshall.")
			return FullStatsAndChallengeInfo{}, err
		}
	}

	stats := []CompositeStat{}
	// convert stats into list for backwards compatability for now
	for _, stat := range userStats.Stats {
		stats = append(stats, stat)
	}

	activeChallenges, fullPoolInfo, err := getChallenges(ctx, logger, nil, nk, userID)

	if err != nil {
		return FullStatsAndChallengeInfo{}, err
	}

	activeMap := map[string]ChallengeProgress{}
	for _, challenge := range activeChallenges.Challenges {
		activeMap[challenge.Name] = ChallengeProgress{Progress: challenge.Progress, Completed: challenge.Completed, Expiry: challenge.Expiry}
	}

	challengesConfig, err := ChallengesConfigWatcher.GetConfigForEnvironment(utils.GetPlayerEnvironment(ctx))

	if err != nil {
		return FullStatsAndChallengeInfo{}, err
	}

	var fullInfo FullStatsAndChallengeInfo = FullStatsAndChallengeInfo{Stats: stats, LastMatchStats: lastMatchStats.LastMatchStats, Challenges: activeMap, LastMatchChallengesPreUpdate: lastMatchStats.ChallengesPreUpdate, LastMatchMapName: lastMatchStats.MapName, LastMatchGameMode: lastMatchStats.GameModeString, LastMatchResult: lastMatchStats.MatchResult, PoolAndSlotInfo: fullPoolInfo}

	fullInfo.ActiveQuestName = challengesConfig.ActiveQuestName

	return fullInfo, nil
}
