package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/getsentry/sentry-go"
	"github.com/heroiclabs/nakama-common/api"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/auth"
	"wildlight.gg/bluejay/configs"
	"wildlight.gg/bluejay/entitlements"
	"wildlight.gg/bluejay/fleetmanager"
	"wildlight.gg/bluejay/matchmaking"
	"wildlight.gg/bluejay/matchstart"
	"wildlight.gg/bluejay/playlists"
	"wildlight.gg/bluejay/progression"
	"wildlight.gg/bluejay/store"
	"wildlight.gg/bluejay/utils"
	"wildlight.gg/bluejay/vivox"
)

var playlistManager *playlists.PlaylistManager
var matchmaker *matchmaking.Matchmaker
var matchTracker *matchmaking.MatchTracker
var matchLauncher *matchstart.MatchLauncher
var playerMetaSystem *utils.PlayerMetaSystem

func InitModule(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, initializer runtime.Initializer) error {
	logger.Info("Bluejay go module initializing")

	env, ok := ctx.Value(runtime.RUNTIME_CTX_ENV).(map[string]string)
	if !ok {
		return errors.New("expected RUNTIME_CTX_ENV to be a map[string]string")
	}

	sentryDsn, found := env["NC_SENTRY_DSN"]
	if found && sentryDsn != "" {
		sampleRate := 0.0
		sentrySampleRate, found := env["NC_SENTRY_SAMPLE_RATE"]
		if found {
			var err error
			sampleRate, err = strconv.ParseFloat(sentrySampleRate, 64)
			if err != nil {
				logger.WithField("error", err).WithField("sampleRate", sentrySampleRate).Error("error parsing Sentry sample rate - setting to 0")
				sampleRate = 0.0
			}
		}
		err := sentry.Init(sentry.ClientOptions{
			Dsn:              sentryDsn,
			EnableTracing:    true,
			TracesSampleRate: sampleRate,
		})
		if err != nil {
			logger.WithField("error", err).WithField("dsn", sentryDsn).WithField("sampleRate", sampleRate).Error("failed to initialize sentry")
		} else {
			logger.Info("Sentry initialized")
			utils.UseSentry = true
		}
	}

	fleetManager, err := fleetmanager.NewWildlightFleetManager(ctx, logger, db, initializer, nk)
	if err != nil {
		logger.WithField("error", err).Error("failed to create fleet manager")
		return err
	}

	err = initializer.RegisterFleetManager(fleetManager)
	if err != nil {
		logger.WithField("error", err).Error("failed to register fleet manager")
		return err
	}

	err = initializer.RegisterEventSessionStart(eventSessionStart)
	if err != nil {
		logger.Error("Error registering EventSessionStart: %v", err)
	}

	err = initializer.RegisterEventSessionEnd(eventSessionEnd)
	if err != nil {
		logger.Error("Error registering EventSessionEnd: %v", err)
	}

	err = initializer.RegisterAfterAuthenticateSteam(authSatori)
	if err != nil {
		logger.Error("Error registering AfterAuthenticateSteam: %v", err)
		return err
	}

	err = initializer.RegisterAfterAuthenticateEmail(authSatori)
	if err != nil {
		logger.Error("Error registering AfterAuthenticateEmail: %v", err)
		return err
	}

	err = auth.RegisterPragmaAuth(ctx, initializer)
	if err != nil {
		logger.Error("Error registering BeforeAuthCustom: %v", err)
		return err
	}

	err = initializer.RegisterMatch("launch", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule) (runtime.Match, error) {
		return &matchstart.LaunchMatch{}, nil
	})
	if err != nil {
		logger.Error("unable to register: %v", err)
		return err
	}

	useSatori := false
	val, found := env["NC_USE_SATORI"]
	if found {
		useSatori, err = strconv.ParseBool(val)
		if err != nil {
			useSatori = false
		}
	}

	if useSatori {
		satori := nk.GetSatori()

		apiKeyName, found := env["NC_SATORI_API_KEY_NAME"]
		if !found || apiKeyName == "" {
			apiKeyName = "default"
		}

		signingKey, found := env["NC_SATORI_SIGNING_KEY"]
		if !found || signingKey == "" {
			return fmt.Errorf("Must specify a Satori signing key in NC_SATORI_SIGNING_KEY")
		}

		utils.InitSatoriTokenGenerator(apiKeyName, signingKey)
		logger.Info("Satori client initialized")

		utils.BindSatoriClient(satori)
	}

	playlistManager, err = playlists.NewPlaylistManager(ctx, logger, nk)
	if err != nil {
		return fmt.Errorf("error initializing playlist manager: %w", err)
	}

	playerMetaSystem = utils.NewPlayerMetaSystem(nk)

	matchTracker, err = matchmaking.NewMatchTracker(ctx, logger, nk, initializer, playlistManager)
	if err != nil {
		return fmt.Errorf("error initializing match tracker: %w", err)
	}

	matchmaker, err = matchmaking.NewMatchmaker(ctx, logger, initializer, playlistManager, playerMetaSystem, matchTracker)
	if err != nil {
		return fmt.Errorf("error initializing matchmaker: %w", err)
	}

	matchLauncher = matchstart.NewMatchLauncher(playlistManager)

	err = initializer.RegisterMatchmakerMatched(matchLauncher.MatchmakerMatched)
	if err != nil {
		logger.Error("Error registering MatchmakerMatched: %v", err)
		return err
	}

	progression.InitProgressionConfigWatcher(nk, logger)
	entitlements.InitEntitlementsConfigWatcher(nk, logger)
	vivox.InitVivoxConfigWatcher(ctx, logger)

	err = configs.CreateWatcher(logger)

	if err != nil {
		return err
	}

	rpcMap := map[string]utils.NakamaRPC{
		// Entitlements
		"getEntitlements":      entitlements.GetEntitlements,
		"getEntitlementsAsMap": entitlements.GetEntitlementsAsMap,
		"getEquippedLoadout":   entitlements.GetFullLoadoutForUser,
		"getFeaturedCharacter": entitlements.GetFeaturedCharacterForUser,
		"getFeaturedMount":     entitlements.GetFeaturedMountForUser,
		"getFeaturedWeapon":    entitlements.GetFeaturedWeaponForUser,
		"getPlayerLoadout":     entitlements.GetPlayerLoadout,
		"grantEntitlements":    entitlements.GrantEntitlements,
		"resetAllUsersLoadout": entitlements.ResetAllUsersLoadout,
		"resetEntitlements":    entitlements.ResetEntitlements,
		"resetUserLoadout":     entitlements.ResetUserLoadout,
		"setCharacterLoadout":  entitlements.SetCharacterLoadoutForUser,
		"setEquippedLoadout":   entitlements.SetFullLoadoutForUser,
		"setFeaturedCharacter": entitlements.SetFeaturedCharacterForUser,
		"setFeaturedMount":     entitlements.SetFeaturedMountForUser,
		"setFeaturedWeapon":    entitlements.SetFeaturedWeaponForUser,
		"setMountLoadout":      entitlements.SetMountLoadoutForUser,
		"setPlayerItems":       entitlements.SetPlayerItems,
		"setPlayerLoadout":     entitlements.SetPlayerLoadout,
		"setPlayerLoadoutSlot": entitlements.SetPlayerLoadoutSlot,
		"setRaidToolsLoadout":  entitlements.SetRaidToolsLoadoutForUser,
		"setWeaponLoadout":     entitlements.SetWeaponLoadoutForUser,
		"setFeaturedCharacterV2": entitlements.SetFeaturedCharacterForPlayer,
		"setFeaturedMountV2":     entitlements.SetFeaturedMountForPlayer,
		"setFeaturedWeaponV2":    entitlements.SetFeaturedWeaponForPlayer,
		"getFeaturedCharacterV2": entitlements.GetFeaturedCharacterForPlayer,
		"getFeaturedMountV2":     entitlements.GetFeaturedMountForPlayer,
		"getFeaturedWeaponV2":    entitlements.GetFeaturedWeaponForPlayer,

		// Progression
		"applyStatsAndChallengesV2":     progression.ApplyStatsAndChallenges,
		"completeChallenges":            progression.CompleteChallenges,
		"loadStatsAndChallengesV2":      progression.LoadStatsAndChallenges,
		"resetPlayerChallenges":         progression.ResetPlayerChallenges,
		"resetPlayerStats":              progression.ResetPlayerStats,
		"resetPlayerStatsAndChallenges": progression.ResetPlayerStatsAndChallenges,

		// Progression old
		"applyStatsAndChallenges":     utils.DeprecatedRPC,
		"getPlayerStatsAndChallenges": progression.GetPlayerStatsAndChallenges,
		"loadStatsAndChallenges":      utils.DeprecatedRPC,
		"setPlayerStatsAndChallenges": progression.SetPlayerStatsAndChallenges,

		// Store
		"getAllBundleIds":         store.GetAllBundleIds,
		"getAllItemIds":           store.GetAllItemIds,
		"getAllStoreSections":     store.GetAllStoreSections,
		"getAllTreasureTroveIds":  store.GetAllTreasureTroveIds,
		"getAllTrovePageIds":      store.GetAllTrovePageIds,
		"getBundlesByIds":         store.GetBundlesByIds,
		"getCurrencyLimits":       store.GetCurrencyLimits,
		"getItemsByIds":           store.GetItemsByIds,
		"getTrovePagesByIds":      store.GetTrovePagesByIds,
		"getStoreSectionsByIds":   store.GetStoreSectionsByIds,
		"getTreasureTrovesByIds":  store.GetTreasureTrovesByIds,
		"getUserWalletInfo":       store.GetUserWalletInfo,
		"getUserWalletLedgerInfo": store.GetUserWalletLedgerInfo,
		"grantCurrency":           store.GrantCurrency,
		"purchaseBundle":          store.PurchaseBundle,
		"purchaseSingleItem":      store.PurchaseSingleItem,
		"purchaseTreasureTrove":   store.PurchaseTreasureTrove,
		"purchaseTrovePageItem":   store.PurchaseTrovePageItem,
		"resetCurrency":           store.ResetCurrency,

		// Matchmaking
		"getPlaylists": getPlaylists,

		// Config getters
		"getClientHotfixConfigs": configs.GetClientHotfixConfigs,
		"getConfig":              configs.GetConfig,
		"getServerHotfixConfigs": configs.GetServerHotfixConfigs,

		//RPC - Token & Block Player
		"getLoginToken":         vivox.GetLoginToken,
		"getJoinToken":          vivox.GetJoinToken,
		"getJoinMutedToken":     vivox.GetJoinMutedToken,
		"getTranscriptionToken": vivox.GetTranscriptionToken,
		"setBlockPlayer":        vivox.SetBlockPlayer,
		"setUnblockPlayer":      vivox.SetUnblockPlayer,
		"getListBlockedPlayers": vivox.GetListBlockedPlayers,
	}

	for key, rpc := range rpcMap {
		err := utils.RegisterWildlightRpc(initializer, logger, key, rpc)
		if err != nil {
			return err
		}
	}

	return nil
}

func authSatori[request any](ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out *api.Session, in *request) error {

	return nil
}

// RPC called by the game client when a user requests playlist configurations
func getPlaylists(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	env := utils.GetPlayerEnvironment(ctx)

	playlists, err := playlistManager.GetClientPlaylistsForEnvironment(env)
	if err != nil {
		logger.WithField("env", env).Error("could not find playlist configuration for environment")
		return "", err
	}

	playlistJson, err := json.Marshal(playlists)
	if err != nil {
		return "", err
	}

	return string(playlistJson), nil
}

func eventSessionStart(ctx context.Context, logger runtime.Logger, evt *api.Event) {
	env := utils.GetPlayerEnvironment(ctx)

	playerMetaSystem.InitializePlayer(ctx, logger)

	userId := utils.GetUserId(ctx)
	playlistManager.OnSessionStart(ctx, logger, userId, env)
	matchTracker.OnSessionStart(ctx, logger, userId, env)

	satori := utils.GetSatori()
	if satori != nil {
		userId := utils.GetUserId(ctx)

		customProperties := map[string]string{
			"environment": utils.GetPlayerEnvironment(ctx),
			"sessionId":   utils.GetSessionId(ctx),
		}
		defaultProperties := map[string]string{
			"version":  utils.GetPlayerBuildVersion(ctx),
			"platform": utils.GetPlayerPlatform(ctx),
		}

		userProperties, err := satori.Authenticate(ctx, userId, defaultProperties, customProperties, false, utils.GetUserIpAddress(ctx))
		if err != nil {
			logger.WithField("err", err).Error("error authenticating in Satori")
		} else {
			logger.WithField("props", userProperties).Info("Authenticated to Satori")
		}
	}
}

func eventSessionEnd(ctx context.Context, logger runtime.Logger, evt *api.Event) {
	logger.WithField("userId", utils.GetUserId(ctx)).WithField("sessionId", utils.GetSessionId(ctx)).Info("Publishing sessionEnd event")
	utils.PublishSessionEndEvent(ctx, logger)
}
