package entitlements

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/heroiclabs/nakama-common/api"
	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/configs"
	"wildlight.gg/bluejay/storage"
	"wildlight.gg/bluejay/utils"
)

type UserEntitlementResponse struct {
	IDs []string
}

type UserIDRequest struct {
	ID string
}

type SectionIDsRequest UserEntitlementResponse
type BundleIDsRequest UserEntitlementResponse
type ItemIDsRequest UserEntitlementResponse
type TreasureTroveIDsRequest UserEntitlementResponse
type TrovePageIDsRequest UserEntitlementResponse
type GrantEntitlementsRequest UserEntitlementResponse
type EntitlementsUnlocked UserEntitlementResponse

type GrantCurrencyPayload struct {
	UserId   string
	Delta    int64
	Currency string
}

type UserEntitlements struct {
	Entitlements map[string]any
}

type FeaturedCharacter struct {
	CharacterIndexID string
}

type FeaturedWeapon struct {
	WeaponIndexID string
}

type FeaturedMount struct {
	MountIndexID string
}

type EquippedLoadout struct {
	FeaturedCharacterIndexId string
	FeaturedWeaponIndexId    string
	FeaturedMountIndexId     string
	Characters               map[string]UserEntitlementResponse
	Weapons                  map[string]UserEntitlementResponse
	Mounts                   map[string]UserEntitlementResponse
	RaidTools                map[string]UserEntitlementResponse
	PlayerItems              []string
}

type LoadoutSlot struct {
	AssociatedActorTag          string
	AssociatedMTXAssetTagPrefix string
	UniqueMTXAssetIDs           []string
}

type PlayerLoadout struct {
	FeaturedCharacterIndexId string
	FeaturedWeaponIndexId    string
	FeaturedMountIndexId     string
	LoadoutSlots             []LoadoutSlot
}

type PlayerLoadoutSlotUpdate struct {
	LoadoutIndex int
	Slot         LoadoutSlot
}

type EquipPayloadForComponent struct {
	ID                    string
	LoadoutEntitlementIDs []string
}

// GetUserInfoPayload Generic request payload to load user info
type GetUserInfoPayload struct {
	UserID string
	Cursor string
}

// PurchaseResult Results of Purchase operation
type PurchaseResult struct {
	StorageUpdates []*api.StorageObjectAck
	WalletUpdates  []*runtime.WalletUpdateResult
}

type EntitlementsConfig struct {
	DefaultEquipmentLoadout EquippedLoadout
	DefaultEntitlements     []string
	EntitlementBlockList    map[string]string
}

var EntitlementsConfigWatcher *configs.ConfigWatcher[EntitlementsConfig]
var EntitlementsConfigPath = "config/game-configs/entitlements/"

func InitEntitlementsConfigWatcher(nk runtime.NakamaModule, logger runtime.Logger) {
	EntitlementsConfigWatcher = configs.NewConfigWatcher(EntitlementsConfigPath, onConfigLoaded, logger)
}

func onConfigLoaded(env string, config EntitlementsConfig) error {

	return nil
}

func writeUserLoadout(ctx context.Context, nk runtime.NakamaModule, userId string, version string, loadout EquippedLoadout) (string, error) {

	if loadoutJSON, err := json.Marshal(loadout); err != nil {
		return "", err
	} else {

		writeRequest := &runtime.StorageWrite{
			Collection:      storage.LOADOUT_COLLECTION,
			Key:             storage.EQUIPPED_LOADOUT,
			UserID:          userId,
			Version:         version,
			Value:           string(loadoutJSON),
			PermissionRead:  1,
			PermissionWrite: 1,
		}

		if _, err := nk.StorageWrite(ctx, []*runtime.StorageWrite{writeRequest}); err != nil {
			return "", err
		} else {
			return "", nil
		}
	}
}

func writePlayerLoadout(ctx context.Context, nk runtime.NakamaModule, userId string, version string, loadout PlayerLoadout) (string, error) {

	if loadoutJSON, err := json.Marshal(loadout); err != nil {
		return "", err
	} else {

		writeRequest := &runtime.StorageWrite{
			Collection:      storage.LOADOUT_COLLECTION,
			Key:             storage.PLAYER_LOADOUT,
			UserID:          userId,
			Version:         version,
			Value:           string(loadoutJSON),
			PermissionRead:  1,
			PermissionWrite: 1,
		}

		if _, err := nk.StorageWrite(ctx, []*runtime.StorageWrite{writeRequest}); err != nil {
			return "", err
		} else {
			return "", nil
		}
	}
}

func ReadUserLoadout(ctx context.Context, nk runtime.NakamaModule, userId string, logger runtime.Logger) (EquippedLoadout, string, error) {

	readRequest := &runtime.StorageRead{
		Collection: storage.LOADOUT_COLLECTION,
		Key:        storage.EQUIPPED_LOADOUT,
		UserID:     userId,
	}

	if readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{readRequest}); err != nil {
		logger.Error(fmt.Sprintf("Got error reading from DB: %s", err))
		return EquippedLoadout{}, "", runtime.NewError("Error read DB error.", 2)
	} else if len(readResult) == 0 {
		// If the user has zero character loadout info Set, initialize the map
		// Then Set all the character keys that are missing (if new, all would be missing, and we Set all, or we Set only the missing ones)
		equippedLoadout, err := populateDefaultInfoIfMissing(EquippedLoadout{}, ctx, logger)

		return equippedLoadout, "", err
	} else {
		var equippedLoadout EquippedLoadout
		if err := json.Unmarshal([]byte(readResult[0].Value), &equippedLoadout); err != nil {
			logger.Error(fmt.Sprintf("Could not unmarshall string into json %s", readResult[0].Value))
			return EquippedLoadout{}, "", err
		}

		equippedLoadout, err = populateDefaultInfoIfMissing(equippedLoadout, ctx, logger)

		return equippedLoadout, readResult[0].Version, err
	}
}

func ReadPlayerLoadout(ctx context.Context, nk runtime.NakamaModule, userId string, logger runtime.Logger) (PlayerLoadout, string, error) {

	readRequest := &runtime.StorageRead{
		Collection: storage.LOADOUT_COLLECTION,
		Key:        storage.PLAYER_LOADOUT,
		UserID:     userId,
	}

	if readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{readRequest}); err != nil {
		logger.Error(fmt.Sprintf("Got error reading from DB: %s", err))
		return PlayerLoadout{}, "", runtime.NewError("Error read DB error.", 2)
	} else if len(readResult) == 0 {

		return PlayerLoadout{}, "", err
	} else {
		var equippedLoadout PlayerLoadout
		if err := json.Unmarshal([]byte(readResult[0].Value), &equippedLoadout); err != nil {
			logger.Error(fmt.Sprintf("Could not unmarshall string into json %s", readResult[0].Value))
			return PlayerLoadout{}, "", err
		}

		return equippedLoadout, readResult[0].Version, err
	}
}

func ReadEntitlementsWithDefaultsPopulated(ctx context.Context, nk runtime.NakamaModule, userId string, logger runtime.Logger) (string, error) {
	if userEntitlements, err := ReadEntitlements(ctx, nk, userId); err != nil {
		logger.Error("Error fetching entitlement details", err)
		return "", err
	} else {
		entitlementsConfig, err := EntitlementsConfigWatcher.GetConfigForEnvironment(utils.GetPlayerEnvironment(ctx))
		if err != nil {
			return "", err
		}
		userEntitlements.IDs = append(userEntitlements.IDs, entitlementsConfig.DefaultEntitlements...)

		if entitlementJson, err := json.Marshal(userEntitlements); err != nil {
			logger.Error("Error formatting JSON string", err)
			return "", err
		} else {

			logger.Debug("Response %s", string(entitlementJson))
			return string(entitlementJson), nil
		}
	}
}

func ReadEntitlements(ctx context.Context, nk runtime.NakamaModule, userID string) (UserEntitlementResponse, error) {

	var uer = UserEntitlementResponse{}

	// Read from new location
	ue, _, err := ReadEntitlementsAsMap(ctx, nk, userID)

	if err != nil {
		return UserEntitlementResponse{}, err
	}

	// If we did find entitlements, convert to list format for compatability
	for key := range ue.Entitlements {
		uer.IDs = append(uer.IDs, key)
	}

	return uer, err

}

func ReadEntitlementsAsMap(ctx context.Context, nk runtime.NakamaModule, userID string) (UserEntitlements, string, error) {
	storageRead := &runtime.StorageRead{
		Collection: storage.ENTITLEMENT_COLLECTION,
		Key:        storage.ENTITLEMENTS,
		UserID:     userID,
	}

	readResult, err := nk.StorageRead(ctx, []*runtime.StorageRead{storageRead})

	if err != nil {
		return UserEntitlements{}, "", err
	}

	if len(readResult) == 0 {
		return UserEntitlements{}, "", nil
	}

	if len(readResult) != 1 {
		return UserEntitlements{}, "", runtime.NewError(fmt.Sprintf("More than one entitlement result found. Must investigate for user id %s", userID), 3)
	}

	result := readResult[0]

	userEntitlement := UserEntitlements{}

	err = json.Unmarshal([]byte(result.Value), &userEntitlement)

	if err != nil {
		return UserEntitlements{}, "", err
	}

	return userEntitlement, result.Version, err
}

// true if user has all entitlements, otherwise false
func HasEntitlements(ctx context.Context, nk runtime.NakamaModule, userID string, entitlements []string, logger runtime.Logger) (bool, error) {

	if len(entitlements) == 0 {
		return true, nil
	}

	logger.Debug(fmt.Sprintf("Checking to see if user has entitlements already: %s", entitlements))

	if userEntitlements, _, err := ReadEntitlementsAsMap(ctx, nk, userID); err != nil {
		return false, err
	} else {

		logger.Debug(fmt.Sprintf("Users existing entitlements: %s", userEntitlements))

		for _, entitlement := range entitlements {
			if _, ok := userEntitlements.Entitlements[entitlement]; !ok {
				return false, nil
			}
		}

		return true, nil
	}
}

func resetUserLoadoutHelper(ctx context.Context, nk runtime.NakamaModule, userId string, logger runtime.Logger) (EquippedLoadout, error) {

	readRequest := &runtime.StorageDelete{
		Collection: storage.LOADOUT_COLLECTION,
		Key:        storage.EQUIPPED_LOADOUT,
		UserID:     userId,
	}

	if err := nk.StorageDelete(ctx, []*runtime.StorageDelete{readRequest}); err != nil {
		logger.Error(fmt.Sprintf("Got error reading from DB: %s", err))
		return EquippedLoadout{}, runtime.NewError("Error read DB error.", 2)
	} else {
		equippedLoadout, err := populateDefaultInfoIfMissing(EquippedLoadout{}, ctx, logger)

		return equippedLoadout, err
	}
}

func ResetAllUsersLoadoutHelper(ctx context.Context, nk runtime.NakamaModule, logger runtime.Logger) error {

	var loadoutCursor string

	userIds := []string{}

	if results, cursor, err := nk.StorageList(ctx, "", "", storage.LOADOUT_COLLECTION, 500, ""); err != nil {
		return err
	} else {
		loadoutCursor = cursor

		loadoutDeletes := []*runtime.StorageDelete{}

		for _, userLoadout := range results {
			logger.Warn("Deleting loadout for user : %s", userLoadout.UserId)
			userIds = append(userIds, userLoadout.UserId)

			deleteRequest := runtime.StorageDelete{
				Collection: storage.LOADOUT_COLLECTION,
				Key:        storage.EQUIPPED_LOADOUT,
				UserID:     userLoadout.UserId,
			}

			loadoutDeletes = append(loadoutDeletes, &deleteRequest)
		}

		for loadoutCursor != "" {
			if results, cursor, err := nk.StorageList(ctx, "", "", storage.LOADOUT_COLLECTION, 500, ""); err != nil {
				return err
			} else {
				loadoutCursor = cursor

				for _, userLoadout := range results {
					deleteRequest := runtime.StorageDelete{
						Collection: storage.LOADOUT_COLLECTION,
						Key:        storage.EQUIPPED_LOADOUT,
						UserID:     userLoadout.UserId,
					}

					loadoutDeletes = append(loadoutDeletes, &deleteRequest)
				}
			}
		}

		if err := nk.StorageDelete(ctx, loadoutDeletes); err != nil {
			return err
		} else {
			logger.Info("Deleted loadout for users : %v", userIds)
			return nil
		}

	}
}

func populateDefaultInfoIfMissing(equippedLoadout EquippedLoadout, ctx context.Context, _ runtime.Logger) (EquippedLoadout, error) {
	entitlementsConfig, err := EntitlementsConfigWatcher.GetConfigForEnvironment(utils.GetPlayerEnvironment(ctx))
	if err != nil {
		return EquippedLoadout{}, err
	}

	defaultLoadout := entitlementsConfig.DefaultEquipmentLoadout

	if len(equippedLoadout.FeaturedCharacterIndexId) == 0 || equippedLoadout.FeaturedCharacterIndexId == "" {
		equippedLoadout.FeaturedCharacterIndexId = defaultLoadout.FeaturedCharacterIndexId
	}

	if equippedLoadout.PlayerItems == nil {
		equippedLoadout.PlayerItems = defaultLoadout.PlayerItems
	}

	if equippedLoadout.Characters == nil {
		// logger.Info("Creating new Characters map for loadout")
		equippedLoadout.Characters = map[string]UserEntitlementResponse{}
	}

	for character, defaultSkin := range defaultLoadout.Characters {
		// logger.Info("Checking if user has key for character %s", character)

		if charLoadout, ok := equippedLoadout.Characters[character]; !ok {
			// logger.Info("User does not have key for %s. Adding default loadout %v", character, defaultSkinLoadout)
			equippedLoadout.Characters[character] = defaultSkin
		} else if len(charLoadout.IDs) == 0 {
			// logger.Info("User does have key for %s but no entitlements, adding array %v", character, skinsArray)
			equippedLoadout.Characters[character] = defaultSkin
		}
	}

	if equippedLoadout.Weapons == nil {
		// logger.Info("Creating new Weapons map for loadout")
		equippedLoadout.Weapons = map[string]UserEntitlementResponse{}
	}

	for weaponId, weaponSkin := range defaultLoadout.Weapons {
		// logger.Info("Checking if user has key for weaponId %s", weaponId)
		if weaponLoadout, ok := equippedLoadout.Weapons[weaponId]; !ok {
			// logger.Info("User does not have key for %s. Adding default loadout %v", weaponId, defaultWeaponLoadout)
			equippedLoadout.Weapons[weaponId] = weaponSkin
		} else if len(weaponLoadout.IDs) == 0 {
			// logger.Info("User does have key for %s but no entitlements, adding array %v", weaponId, skinsArray)
			equippedLoadout.Weapons[weaponId] = weaponSkin
		}
	}

	if equippedLoadout.Mounts == nil {
		equippedLoadout.Mounts = map[string]UserEntitlementResponse{}
	}

	for mountId, mountSkin := range defaultLoadout.Mounts {
		if mountLoadout, ok := equippedLoadout.Mounts[mountId]; !ok {
			equippedLoadout.Mounts[mountId] = mountSkin
		} else if len(mountLoadout.IDs) == 0 {
			equippedLoadout.Mounts[mountId] = mountSkin
		}
	}

	return equippedLoadout, nil
}

func grantUserEntitlementDebugAPI(ctx context.Context, nk runtime.NakamaModule, entitlements []string, userID string) (*runtime.StorageWrite, error) {

	userEntitlements, version, err := ReadEntitlementsAsMap(ctx, nk, userID)

	if err != nil {
		return nil, err
	}

	for _, entitlement := range entitlements {
		userEntitlements.Entitlements[entitlement] = struct{}{}
	}

	newEntitlements, err := json.Marshal(userEntitlements)

	if err != nil {
		return nil, err
	}

	storageWrite := &runtime.StorageWrite{
		Collection:      storage.ENTITLEMENT_COLLECTION,
		Key:             storage.ENTITLEMENTS,
		UserID:          userID,
		PermissionRead:  1,
		PermissionWrite: 1,
		Value:           string(newEntitlements),
		Version:         version,
	}

	return storageWrite, nil
}
