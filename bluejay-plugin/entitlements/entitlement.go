package entitlements

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/heroiclabs/nakama-common/runtime"
	"wildlight.gg/bluejay/errors"
	"wildlight.gg/bluejay/storage"
	"wildlight.gg/bluejay/utils"
)

func SetCharacterLoadoutForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return SetComponentLoadout(ctx, logger, payload, nk, "characters")
}

func SetWeaponLoadoutForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return SetComponentLoadout(ctx, logger, payload, nk, "weapons")
}

func SetMountLoadoutForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return SetComponentLoadout(ctx, logger, payload, nk, "mounts")
}

func SetRaidToolsLoadoutForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return SetComponentLoadout(ctx, logger, payload, nk, "raidtools")
}

func SetPlayerItems(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	return SetComponentLoadout(ctx, logger, payload, nk, "playeritems")
}

func SetComponentLoadout(ctx context.Context, logger runtime.Logger, payload string, nk runtime.NakamaModule, component string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &EquipPayloadForComponent{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Got Set %s loadout request %v", component, *request))

		if currentLoadout, version, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {

			updatedLoadout := UserEntitlementResponse{IDs: request.LoadoutEntitlementIDs}

			logger.Info(fmt.Sprintf("Setting updated/trimmed %s ids %v", component, updatedLoadout))

			switch component {
			case "raidtools":
				if currentLoadout.RaidTools == nil {
					currentLoadout.RaidTools = map[string]UserEntitlementResponse{}
				}
				currentLoadout.RaidTools[request.ID] = updatedLoadout
			case "weapons":
				if currentLoadout.Weapons == nil {
					currentLoadout.Weapons = map[string]UserEntitlementResponse{}
				}
				currentLoadout.Weapons[request.ID] = updatedLoadout
			case "characters":
				if currentLoadout.Characters == nil {
					currentLoadout.Characters = map[string]UserEntitlementResponse{}
				}
				currentLoadout.Characters[request.ID] = updatedLoadout
			case "mounts":
				if currentLoadout.Mounts == nil {
					currentLoadout.Mounts = map[string]UserEntitlementResponse{}
				}
				currentLoadout.Mounts[request.ID] = updatedLoadout
			case "playeritems":
				if currentLoadout.PlayerItems == nil {
					currentLoadout.PlayerItems = []string{}
				}
				currentLoadout.PlayerItems = updatedLoadout.IDs
			}

			return writeUserLoadout(ctx, nk, userId, version, currentLoadout)
		}
	}
}

func SetFullLoadoutForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &EquippedLoadout{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Got loadout info %v", *request))

		if _, version, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {

			return writeUserLoadout(ctx, nk, userId, version, *request)
		}

	}
}

func GetFullLoadoutForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		if loadout, _, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {

			if fullLoadout, err := json.Marshal(loadout); err != nil {
				logger.Error("Error formatting JSON string", err)
				return "", err
			} else {
				return string(fullLoadout), nil
			}
		}

	}
}

func SetFeaturedCharacterForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &FeaturedCharacter{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Setting featured char for user: %s for %s", request.CharacterIndexID, userId))

		if loadout, version, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {
			loadout.FeaturedCharacterIndexId = request.CharacterIndexID

			return writeUserLoadout(ctx, nk, userId, version, loadout)
		}

	}
}

func GetFeaturedCharacterForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		if loadout, _, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {
			var response FeaturedCharacter = FeaturedCharacter{CharacterIndexID: loadout.FeaturedCharacterIndexId}

			if featuredCharacter, err := json.Marshal(response); err != nil {
				logger.Error("Error formatting JSON string", err)
				return "", err
			} else {
				return string(featuredCharacter), nil
			}
		}

	}
}

func SetFeaturedWeaponForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &FeaturedWeapon{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Setting featured char for user: %s for %s", request.WeaponIndexID, userId))

		if loadout, version, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {
			loadout.FeaturedWeaponIndexId = request.WeaponIndexID

			return writeUserLoadout(ctx, nk, userId, version, loadout)
		}

	}
}

func GetFeaturedWeaponForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		if loadout, _, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {
			var response FeaturedWeapon = FeaturedWeapon{WeaponIndexID: loadout.FeaturedWeaponIndexId}

			if featuredWeapon, err := json.Marshal(response); err != nil {
				logger.Error("Error formatting JSON string", err)
				return "", err
			} else {
				return string(featuredWeapon), nil
			}
		}

	}
}

func SetFeaturedMountForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &FeaturedMount{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Setting featured char for user: %s for %s", request.MountIndexID, userId))

		if loadout, version, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {
			loadout.FeaturedMountIndexId = request.MountIndexID

			return writeUserLoadout(ctx, nk, userId, version, loadout)
		}

	}
}

func GetFeaturedMountForUser(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		if loadout, _, err := ReadUserLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {
			var response FeaturedMount = FeaturedMount{MountIndexID: loadout.FeaturedMountIndexId}

			if featuredMount, err := json.Marshal(response); err != nil {
				logger.Error("Error formatting JSON string", err)
				return "", err
			} else {
				return string(featuredMount), nil
			}
		}

	}
}

func GetEntitlements(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		return ReadEntitlementsWithDefaultsPopulated(ctx, nk, userId, logger)
	}
}

func GetEntitlementsAsMap(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		uer, _, err := ReadEntitlementsAsMap(ctx, nk, userId)

		if err != nil {
			return "", err
		}

		response, err := json.Marshal(uer)

		return string(response), err
	}
}

func GrantEntitlements(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		request := &GrantEntitlementsRequest{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		if len(request.IDs) == 0 {
			return "", errors.ErrorLoadingData
		}

		if storageWriteUpdates, err := grantUserEntitlementDebugAPI(ctx, nk, request.IDs, userId); err != nil {
			return "", err
		} else {
			if storageAcks, err := nk.StorageWrite(ctx, []*runtime.StorageWrite{storageWriteUpdates}); err != nil {
				logger.WithField("err", err).Error("Multi update error.")
				return "", err
			} else {
				logger.Info("Storage Acks: %d", len(storageAcks))

				if updateJSON, err := json.Marshal(storageAcks); err != nil {
					logger.Error("Error formatting JSON string", err)
					return "", err
				} else {
					jsonString := string(updateJSON)
					return jsonString, nil
				}
			}
		}
	}
}

func ResetEntitlements(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		logger.Warn("Clearing all entitlements for user.", userId)

		deleteAllEntitlements := &runtime.StorageDelete{
			Collection: storage.ENTITLEMENT_COLLECTION,
			Key:        storage.ENTITLEMENTS,
			UserID:     userId,
		}

		troveCurrencyDelete := &runtime.StorageDelete{
			Collection: storage.TROVE_CURRENCY_COLLECTION,
			Key:        storage.TROVE_CURRENCY,
			UserID:     userId,
		}

		err := nk.StorageDelete(ctx, []*runtime.StorageDelete{deleteAllEntitlements, troveCurrencyDelete})

		if err != nil {
			return "", err
		}

		return "", nil
	}
}

func ResetUserLoadout(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		if loadout, err := resetUserLoadoutHelper(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {
			if response, err := json.Marshal(loadout); err != nil {
				return "", err
			} else {
				return string(response), nil
			}
		}
	}
}

func ResetUserLoadoutV2(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	if userId, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string); !ok {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		readRequest := &runtime.StorageDelete{
			Collection: storage.LOADOUT_COLLECTION,
			Key:        storage.PLAYER_LOADOUT,
			UserID:     userId,
		}

		if err := nk.StorageDelete(ctx, []*runtime.StorageDelete{readRequest}); err != nil {
			logger.Error(fmt.Sprintf("Got error reading from DB: %s", err))
			return "", err
		} else {

			return "", nil
		}
	}
}

func ResetAllUsersLoadout(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {

	if err := ResetAllUsersLoadoutHelper(ctx, nk, logger); err != nil {
		return "", err
	} else {
		return "", nil
	}

}

func SetPlayerLoadout(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &PlayerLoadout{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Got loadout info %v", *request))

		if _, version, err := ReadPlayerLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {

			return writePlayerLoadout(ctx, nk, userId, version, *request)
		}

	}
}

func SetPlayerLoadoutSlot(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &PlayerLoadoutSlotUpdate{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Got loadout info %v", *request))

		if playerLoadout, version, err := ReadPlayerLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {
			if request.LoadoutIndex < len(playerLoadout.LoadoutSlots) {
				playerLoadout.LoadoutSlots[request.LoadoutIndex] = request.Slot
				return writePlayerLoadout(ctx, nk, userId, version, playerLoadout)
			} else {
				return "", errors.InvalidPayload
			}

		}

	}
}

func SetFeaturedCharacterForPlayer(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &FeaturedCharacter{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Got loadout info %v", *request))

		return setFeaturedComponentForPlayer(ctx, logger, nk, userId, "character", request.CharacterIndexID)
	}
}

func SetFeaturedWeaponForPlayer(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &FeaturedWeapon{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Got loadout info %v", *request))

		return setFeaturedComponentForPlayer(ctx, logger, nk, userId, "weapon", request.WeaponIndexID)
	}
}

func SetFeaturedMountForPlayer(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		request := &FeaturedMount{}
		if err := json.Unmarshal([]byte(payload), &request); err != nil {
			logger.Error(fmt.Sprintf("Error parsing payload. Error %s", err.Error()), err)
			return "", errors.InvalidPayload
		}

		logger.Info(fmt.Sprintf("Got loadout info %v", *request))

		return setFeaturedComponentForPlayer(ctx, logger, nk, userId, "mount", request.MountIndexID)
	}
}

func setFeaturedComponentForPlayer(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, userId string, component string, newTag string) (string, error) {

	if playerLoadout, version, err := ReadPlayerLoadout(ctx, nk, userId, logger); err != nil {
		return "", err
	} else {

		switch component {
		case "character":
			playerLoadout.FeaturedCharacterIndexId = newTag
		case "weapon":
			playerLoadout.FeaturedWeaponIndexId = newTag
		case "mount":
			playerLoadout.FeaturedMountIndexId = newTag
		default:
			return "", errors.InvalidPayload
		}

		return writePlayerLoadout(ctx, nk, userId, version, playerLoadout)

	}
}

func GetFeaturedMountForPlayer(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		return getFeaturedComponentForPlayer(ctx, logger, nk, userId, "mount")
	}
}

func GetFeaturedCharacterForPlayer(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		return getFeaturedComponentForPlayer(ctx, logger, nk, userId, "character")
	}
}

func GetFeaturedWeaponForPlayer(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {

		return getFeaturedComponentForPlayer(ctx, logger, nk, userId, "weapon")
	}
}

func getFeaturedComponentForPlayer(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, userId string, component string) (string, error) {

	if playerLoadout, _, err := ReadPlayerLoadout(ctx, nk, userId, logger); err != nil {
		return "", err
	} else {
		var response any
		switch component {
		case "character":
			response = FeaturedCharacter{playerLoadout.FeaturedCharacterIndexId}
		case "weapon":
			response = FeaturedWeapon{playerLoadout.FeaturedWeaponIndexId}
		case "mount":
			response = FeaturedMount{playerLoadout.FeaturedMountIndexId}
		default:
			return "", errors.InvalidPayload
		}

		responseContent, err := json.Marshal(response)
		if err != nil {
			return "", err
		}

		return string(responseContent), nil

	}
}

func GetPlayerLoadout(ctx context.Context, logger runtime.Logger, _ *sql.DB, nk runtime.NakamaModule, _ string) (string, error) {
	userId := utils.GetUserId(ctx)
	if len(userId) == 0 {
		logger.Error("No userId found")
		return "", errors.NoUserIdFound
	} else {
		if loadout, _, err := ReadPlayerLoadout(ctx, nk, userId, logger); err != nil {
			return "", err
		} else {

			if fullLoadout, err := json.Marshal(loadout); err != nil {
				logger.Error("Error formatting JSON string", err)
				return "", err
			} else {
				return string(fullLoadout), nil
			}
		}

	}
}
